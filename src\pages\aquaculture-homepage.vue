<template>
  <div class="aquaculture-dashboard">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <h1 class="main-title">广西银海集团智能化养殖平台</h1>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 天气情况 -->
        <div class="panel">
          <div class="panel-header">
            <h3 class="panel-title">区域天气情况</h3>
          </div>
          <WeatherWidget />
        </div>

        <!-- 生产情况 -->
        <div class="panel h-0 flex-1">
          <div class="panel-header">
            <h3 class="panel-title">生产情况</h3>
          </div>
          <ProductionTable :building-id="currentBuildingId" />
        </div>

        <!-- 水质监测 -->
        <div class="panel chart-panel flex-2">
          <div class="panel-header">
            <h3 class="panel-title">水质监测</h3>
          </div>
          <WaterQualityMonitor :building-id="currentBuildingId" />
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="center-section">
        <!-- 统计信息 -->
        <div class="panel workshop-environment">
          <h3 class="panel-title">年度累计统计</h3>
          <div class="stats-display">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-flash text-yellow-400"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">总耗电量</div>
                <div class="stat-value">{{ yearlyStats.totalPower }}kWh</div>
              </div>
            </div>

            <div class="environment-divider"></div>

            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-food text-green-400"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">投喂量</div>
                <div class="stat-value">{{ yearlyStats.totalFeed }}kg</div>
              </div>
            </div>

            <div class="environment-divider"></div>

            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-scale text-blue-400"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">总产量</div>
                <div class="stat-value">{{ yearlyStats.totalOutput }}kg</div>
              </div>
            </div>

            <div class="environment-divider"></div>

            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-water-pump text-cyan-400"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">抽水量</div>
                <div class="stat-value">{{ yearlyStats.totalPumpIn }}m³</div>
              </div>
            </div>

            <div class="environment-divider"></div>

            <div class="stat-item">
              <div class="stat-icon">
                <i class="i-mdi-pipe text-purple-400"></i>
              </div>
              <div class="stat-info">
                <div class="stat-label">排水量</div>
                <div class="stat-value">{{ yearlyStats.totalPumpOut }}m³</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 9栋楼 -->
        <div class="panel buildings-panel">
          <h3 class="panel-title">楼栋选择</h3>
          <div class="buildings-grid">
            <div
              v-for="building in buildings"
              :key="building.id"
              :class="[
                'building-item',
                { active: building.id === currentBuildingId },
              ]"
              @click="selectBuilding(building.id)"
            >
              <div class="building-number">{{ building.name }}</div>
            </div>
          </div>
        </div>

        <!-- 监控画面 -->
        <div class="panel video-panel">
          <h3 class="panel-title">监控画面</h3>
          <div class="video-content">
            <div class="monitor-grid">
              <div class="monitor-item">
                <div class="monitor-title">生产楼宇监控</div>
                <div class="monitor-screen">
                  <img
                    src="https://picsum.photos/300/200?random=1"
                    alt="生产楼宇监控"
                  />
                </div>
              </div>
              <div class="monitor-item">
                <div class="monitor-title">园区监控</div>
                <div class="monitor-screen">
                  <img
                    src="https://picsum.photos/300/200?random=2"
                    alt="园区监控"
                  />
                </div>
              </div>
              <div class="monitor-item">
                <div class="monitor-title">闸坝监控</div>
                <div class="monitor-screen">
                  <img
                    src="https://picsum.photos/300/200?random=3"
                    alt="闸坝监控"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 设备状态 -->
        <div class="panel">
          <div class="panel-header">
            <h3 class="panel-title">设备状态</h3>
          </div>
          <DeviceStatus :building-id="currentBuildingId" />
        </div>

        <!-- 虾苗生长趋势 -->
        <div class="panel chart-panel">
          <div class="panel-header">
            <h3 class="panel-title">虾苗生长趋势</h3>
          </div>
          <ShrimpGrowthTrend :building-id="currentBuildingId" />
        </div>

        <!-- 水下高清摄像头 -->
        <div class="panel">
          <h3 class="panel-title">水下高清摄像头</h3>
          <div class="underwater-camera">
            <img
              src="https://picsum.photos/400/300?random=4"
              alt="水下高清摄像头"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import dayjs from "dayjs";
import WeatherWidget from "../components/WeatherWidget.vue";
import ProductionTable from "../components/ProductionTable.vue";
import WaterQualityMonitor from "../components/WaterQualityMonitor.vue";
import DeviceStatus from "../components/DeviceStatus.vue";
import ShrimpGrowthTrend from "../components/ShrimpGrowthTrend.vue";

// 当前时间
const currentTime = ref("");
const updateTime = () => {
  currentTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss");
};

// 当前选中的楼栋
const currentBuildingId = ref(1);

// 年度统计数据
const yearlyStats = ref({
  totalPower: "1,234,567",
  totalFeed: "456,789",
  totalOutput: "789,123",
  totalPumpIn: "2,345,678",
  totalPumpOut: "1,876,543",
});

// 9栋楼数据
const buildings = ref([
  {
    id: 1,
    name: "1#楼",
    currentPower: 125.6,
    poolCount: 24,
    expectedOutput: 15600,
  },
  {
    id: 2,
    name: "2#楼",
    currentPower: 132.4,
    poolCount: 24,
    expectedOutput: 16200,
  },
  {
    id: 3,
    name: "3#楼",
    currentPower: 118.9,
    poolCount: 20,
    expectedOutput: 14800,
  },
  {
    id: 4,
    name: "4#楼",
    currentPower: 145.2,
    poolCount: 28,
    expectedOutput: 17400,
  },
  {
    id: 5,
    name: "5#楼",
    currentPower: 128.7,
    poolCount: 24,
    expectedOutput: 15900,
  },
  {
    id: 6,
    name: "6#楼",
    currentPower: 135.1,
    poolCount: 26,
    expectedOutput: 16800,
  },
  {
    id: 7,
    name: "7#楼",
    currentPower: 122.3,
    poolCount: 22,
    expectedOutput: 15200,
  },
  {
    id: 8,
    name: "8#楼",
    currentPower: 140.8,
    poolCount: 26,
    expectedOutput: 17100,
  },
  {
    id: 9,
    name: "9#楼",
    currentPower: 129.5,
    poolCount: 24,
    expectedOutput: 16000,
  },
]);

// 选择楼栋
const selectBuilding = (buildingId: number) => {
  currentBuildingId.value = buildingId;
};

// 定时器
let timeTimer: number;
let buildingTimer: number;

onMounted(() => {
  // 更新时间
  updateTime();
  timeTimer = setInterval(updateTime, 1000);

  // 定时切换楼栋
  buildingTimer = setInterval(() => {
    const nextId =
      currentBuildingId.value >= 9 ? 1 : currentBuildingId.value + 1;
    currentBuildingId.value = nextId;
  }, 10000); // 每10秒切换一次
});

onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer);
  if (buildingTimer) clearInterval(buildingTimer);
});
</script>

<style scoped>
.aquaculture-dashboard {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #0efcff;
  font-family: "Microsoft YaHei", sans-serif;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.aquaculture-dashboard::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(14, 252, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(0, 255, 136, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 60%,
      rgba(255, 107, 107, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

/* 顶部标题栏 */
.header-section {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 40px;
  background: linear-gradient(
    90deg,
    rgba(14, 252, 255, 0.1) 0%,
    rgba(14, 252, 255, 0.05) 50%,
    rgba(14, 252, 255, 0.1) 100%
  );
  border-bottom: 2px solid rgba(14, 252, 255, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

.header-section::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #0efcff 50%,
    transparent 100%
  );
  box-shadow: 0 0 10px #0efcff;
}

.main-title {
  font-size: 32px;
  font-weight: bold;
  color: #0efcff;
  text-shadow: 0 0 20px rgba(14, 252, 255, 0.8);
  letter-spacing: 2px;
}

/* 主内容区域 */
.main-content {
  height: 0;
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.left-section,
.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.center-section {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 面板通用样式 */
.panel {
  background: linear-gradient(
    135deg,
    rgba(14, 252, 255, 0.1) 0%,
    rgba(14, 252, 255, 0.05) 100%
  );
  border: 1px solid rgba(14, 252, 255, 0.3);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #0efcff 50%,
    transparent 100%
  );
  box-shadow: 0 0 10px #0efcff;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.panel-title {
  font-size: 18px;
  font-weight: bold;
  color: #0efcff;
  text-shadow: 0 0 10px rgba(14, 252, 255, 0.8);
  text-align: center;
  flex-shrink: 0;
}

/* 统计信息样式 */
.workshop-environment {
  flex-shrink: 0;
  height: auto;
  min-height: 120px;
}

.stats-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  flex: 1;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(
    135deg,
    rgba(14, 252, 255, 0.15) 0%,
    rgba(14, 252, 255, 0.08) 100%
  );
  border: 1px solid rgba(14, 252, 255, 0.3);
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(14, 252, 255, 0.2) 0%,
    transparent 70%
  );
  animation: iconGlow 2s ease-in-out infinite alternate;
}

@keyframes iconGlow {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.stat-icon i {
  font-size: 20px;
  position: relative;
  z-index: 1;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 14px;
  color: #0efcff;
  opacity: 0.8;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #00ff88;
  text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
  animation: valueGlow 3s ease-in-out infinite;
}

@keyframes valueGlow {
  0%,
  100% {
    text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
  }
  50% {
    text-shadow: 0 0 25px rgba(0, 255, 136, 1);
  }
}

.environment-divider {
  width: 2px;
  height: 60px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    #0efcff 20%,
    #0efcff 80%,
    transparent 100%
  );
  position: relative;
  flex-shrink: 0;
}

.environment-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #0efcff;
  border-radius: 50%;
  box-shadow: 0 0 10px #0efcff;
  animation: dividerPulse 2s ease-in-out infinite;
}

@keyframes dividerPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.7;
  }
}

/* 楼栋选择样式 */
.buildings-panel {
  flex: 1;
}

.buildings-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  flex: 1;
}

.building-item {
  background: linear-gradient(
    135deg,
    rgba(14, 252, 255, 0.1) 0%,
    rgba(14, 252, 255, 0.05) 100%
  );
  border: 2px solid rgba(14, 252, 255, 0.3);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 60px;
}

.building-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(14, 252, 255, 0.1) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.building-item:hover::before {
  transform: translateX(100%);
}

.building-item:hover {
  border-color: #0efcff;
  box-shadow: 0 0 20px rgba(14, 252, 255, 0.5);
  transform: scale(1.05);
}

.building-item.active {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 136, 0.2) 0%,
    rgba(0, 255, 136, 0.1) 100%
  );
  border-color: #00ff88;
  box-shadow: 0 0 25px rgba(0, 255, 136, 0.6);
  animation: activeBuilding 2s infinite;
}

@keyframes activeBuilding {
  0%,
  100% {
    box-shadow: 0 0 25px rgba(0, 255, 136, 0.6);
  }
  50% {
    box-shadow: 0 0 35px rgba(0, 255, 136, 0.8);
  }
}

.building-number {
  font-size: 18px;
  font-weight: bold;
  color: #0efcff;
  text-shadow: 0 0 10px rgba(14, 252, 255, 0.8);
}

.building-item.active .building-number {
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.building-popup {
  position: absolute;
  top: -120px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid #00ff88;
  border-radius: 8px;
  padding: 15px;
  min-width: 200px;
  z-index: 10;
}

.popup-content h4 {
  margin: 0 0 10px 0;
  color: #00ff88;
  text-align: center;
}

.popup-stats div {
  margin: 5px 0;
  font-size: 14px;
  color: #0efcff;
}

/* 监控画面样式 */
.video-panel {
  flex: 1;
}

.video-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  flex: 1;
}

.monitor-item {
  display: flex;
  flex-direction: column;
}

.monitor-title {
  text-align: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #0efcff;
}

.monitor-screen {
  flex: 1;
  border: 1px solid rgba(14, 252, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.monitor-screen img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8) contrast(1.2);
}

/* 水下摄像头样式 */
.underwater-camera {
  height: 200px;
  border: 1px solid rgba(14, 252, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.underwater-camera img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8) contrast(1.2);
}

/* 图表面板 */
.chart-panel {
  flex: 1;
  height: 0;
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.panel {
  animation: fadeInUp 0.6s ease-out;
}

.panel:nth-child(1) {
  animation-delay: 0.1s;
}

.panel:nth-child(2) {
  animation-delay: 0.2s;
}

.panel:nth-child(3) {
  animation-delay: 0.3s;
}
</style>
